<script setup lang="ts">
import { ref, watch } from "vue";
import type { ChargeDetailItem } from "../../types/chargeDetail";
import { formatDateTime } from "../../utils/common";
import { chargeTypeMap } from "../../utils/const";

const props = defineProps<{
  visible: boolean;
  chargeDetail: ChargeDetailItem | null;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

const dialogVisible = ref(false);

// 监听props变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal;
  }
);

// 监听内部状态变化
watch(dialogVisible, (newVal) => {
  emit("update:visible", newVal);
});

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false;
};

// 格式化金额
const formatCurrency = (amount: string | null | undefined) => {
  if (!amount) return "--";
  return `¥${parseFloat(amount).toLocaleString()}`;
};

// 格式化账期
const formatChargeMonth = (month: number | null | undefined) => {
  if (!month) return "--";
  const monthStr = month.toString();
  if (monthStr.length === 6) {
    return `${monthStr.substring(0, 4)}-${monthStr.substring(4, 6)}`;
  }
  return monthStr;
};
</script>

<template>
  <Dialog
    v-model:visible="dialogVisible"
    modal
    :header="`收入权责详情 - ${chargeDetail?.order_no || ''}`"
    :style="{ width: '80rem' }"
    class="apple-charge-detail-dialog"
    :closable="true"
    :dismissableMask="true"
    @hide="closeDialog"
    maximizable
  >
    <div v-if="chargeDetail" class="charge-detail-content">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-info-circle"></i>
          基本信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>订单编号</label>
              <span class="detail-value">{{ chargeDetail.order_no }}</span>
            </div>
            <div class="detail-item">
              <label>子订单编号</label>
              <span class="detail-value">{{ chargeDetail.sub_order_no }}</span>
            </div>
            <div class="detail-item">
              <label>客户名称</label>
              <span class="detail-value">{{ chargeDetail.customer_name }}</span>
            </div>
            <div class="detail-item">
              <label>销售</label>
              <span class="detail-value">{{ chargeDetail.sale_name }}</span>
            </div>
            <div class="detail-item">
              <label>签约主体</label>
              <span class="detail-value">{{
                chargeDetail.sign_contract_entity
              }}</span>
            </div>
            <div class="detail-item">
              <label>合同编号</label>
              <span class="detail-value">{{ chargeDetail.contract_num }}</span>
            </div>
            <div class="detail-item">
              <label>合同法务编号</label>
              <span class="detail-value">{{
                chargeDetail.contract_legal_num
              }}</span>
            </div>
            <div class="detail-item">
              <label>客户编号</label>
              <span class="detail-value">{{ chargeDetail.customer_num }}</span>
            </div>
            <div class="detail-item">
              <label>分账序号</label>
              <span class="detail-value">{{ chargeDetail.account_seq }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 权责信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-calendar"></i>
          权责信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>权责账期</label>
              <span class="detail-value">{{
                formatChargeMonth(chargeDetail.charge_month)
              }}</span>
            </div>
            <div class="detail-item">
              <label>调整账期</label>
              <span class="detail-value">{{
                formatChargeMonth(chargeDetail.adjust_month)
              }}</span>
            </div>
            <div class="detail-item">
              <label>订单开始时间</label>
              <span class="detail-value">{{
                chargeDetail.order_charge_begin
              }}</span>
            </div>
            <div class="detail-item">
              <label>订单结束时间</label>
              <span class="detail-value">{{
                chargeDetail.order_charge_end
              }}</span>
            </div>
            <div class="detail-item">
              <label>调账分类</label>
              <span class="detail-value">{{
                chargeDetail.adjust_reason_class || "--"
              }}</span>
            </div>
            <div class="detail-item">
              <label>调账原因</label>
              <span class="detail-value">{{
                chargeDetail.adjust_reason || "--"
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 费用信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-dollar"></i>
          费用信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>权责金额</label>
              <span class="detail-value">{{
                formatCurrency(chargeDetail.fee_amount)
              }}</span>
            </div>
            <div class="detail-item">
              <label>已核销金额</label>
              <span class="detail-value">{{
                formatCurrency(chargeDetail.pay_amount)
              }}</span>
            </div>
            <div class="detail-item">
              <label>未核销金额</label>
              <span class="detail-value">{{
                formatCurrency(chargeDetail.unpay_amount)
              }}</span>
            </div>
            <div class="detail-item">
              <label>税率</label>
              <span class="detail-value">{{ chargeDetail.tax }}%</span>
            </div>
            <div class="detail-item">
              <label>计费类型</label>
              <span class="detail-value">{{ chargeDetail.fee_type }}</span>
            </div>
            <div class="detail-item">
              <label>付费方式</label>
              <span class="detail-value">{{ chargeDetail.pay_type }}</span>
            </div>
            <div class="detail-item">
              <label>币种</label>
              <span class="detail-value">{{ chargeDetail.currency_type }}</span>
            </div>
            <div class="detail-item">
              <label>收入分类</label>
              <span class="detail-value">{{ chargeDetail.income_type }}</span>
            </div>
            <div class="detail-item">
              <label>税率类型</label>
              <span class="detail-value">{{ chargeDetail.tax_type }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 产品信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-box"></i>
          产品信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>产品主类</label>
              <span class="detail-value">{{
                chargeDetail.product_main_category
              }}</span>
            </div>
            <div class="detail-item">
              <label>产品次类</label>
              <span class="detail-value">{{
                chargeDetail.product_sub_category
              }}</span>
            </div>
            <div class="detail-item">
              <label>速率</label>
              <span class="detail-value">{{ chargeDetail.speed }}</span>
            </div>
            <div class="detail-item">
              <label>计费说明</label>
              <span class="detail-value">{{
                chargeDetail.charge_explain
              }}</span>
            </div>
            <div class="detail-item">
              <label>A端信息</label>
              <span class="detail-value">{{ chargeDetail.a_info }}</span>
            </div>
            <div class="detail-item">
              <label>账单模板</label>
              <span class="detail-value">{{ chargeDetail.bill_template }}</span>
            </div>
          </div>
        </Fluid>
      </div>

      <!-- 其他信息 -->
      <div class="detail-section">
        <h3 class="section-title">
          <i class="pi pi-cog"></i>
          其他信息
        </h3>
        <Fluid>
          <div class="grid grid-cols-3 gap-3">
            <div class="detail-item">
              <label>订单类型</label>
              <span class="detail-value">{{ chargeDetail.order_type }}</span>
            </div>
            <div class="detail-item">
              <label>当年业务属性</label>
              <span class="detail-value">{{ chargeDetail.current_attr }}</span>
            </div>
            <div class="detail-item">
              <label>批次号</label>
              <span class="detail-value">{{ chargeDetail.batch_no }}</span>
            </div>
            <div class="detail-item">
              <label>操作员</label>
              <span class="detail-value">{{ chargeDetail.charge_user }}</span>
            </div>
            <div class="detail-item">
              <label>出账类型</label>
              <span class="detail-value">{{ chargeTypeMap[chargeDetail.charge_type] }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间</label>
              <span class="detail-value">{{
                formatDateTime(chargeDetail.created_at)
              }}</span>
            </div>
          </div>
        </Fluid>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <Button
          label="关闭"
          icon="pi pi-times"
          severity="secondary"
          outlined
          @click="closeDialog"
          class="apple-button"
        />
      </div>
    </template>
  </Dialog>
</template>

<style scoped>
/* Apple Design System - 收入权责详情弹框样式 */
:deep(.apple-charge-detail-dialog) {
  border-radius: 16px !important;
  overflow: hidden !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15) !important;
}

:deep(.apple-charge-detail-dialog .p-dialog-header) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08) !important;
  padding: 1.5rem 2rem !important;
  border-radius: 16px 16px 0 0 !important;
}

:deep(.apple-charge-detail-dialog .p-dialog-header .p-dialog-title) {
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  color: #1d1d1f !important;
}

:deep(.apple-charge-detail-dialog .p-dialog-content) {
  padding: 2rem !important;
  background: #ffffff !important;
}

.charge-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1d1d1f;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--p-primary-color);
}

.section-title i {
  color: var(--p-primary-color);
  font-size: 1.2rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #6e6e73;
  margin: 0;
}

.detail-value {
  font-size: 0.95rem;
  color: #1d1d1f;
  font-weight: 400;
  padding: 0.5rem 0.75rem;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem 2rem;
  border-radius: 0 0 16px 16px;
}

.apple-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  padding: 0.75rem 1.5rem !important;
  transition: all 0.2s ease !important;
}

.apple-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 滚动条样式 */
.charge-detail-content::-webkit-scrollbar {
  width: 6px;
}

.charge-detail-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.charge-detail-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.charge-detail-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
