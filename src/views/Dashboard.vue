<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from "../stores/user";
import { logout, checkAuth } from "../services/auth";
import type { Menu } from "../services/auth";
import Footer from "../components/Footer.vue";

const router = useRouter();
const userStore = useUserStore();
const sidebarCollapsed = ref(true);
const menuRef = ref(null);
const menuItems = ref<any[]>([]);
const route = useRoute();
const activeSubmenu = ref<number | null>(null);
const popoverPosition = ref({ top: 0, left: 0 });
const hoveredItem = ref<number | null>(null);
const hoverTimer = ref<number | null>(null);

const transformMenus = (menus: Menu[]) => {
  return menus.map((menu) => ({
    label: menu.name,
    icon: menu.icon,
    tooltip: menu.name,
    operation: menu.operation,
    _open: false,
    items: (menu.children || []).map((child) => ({
      label: child.name,
      icon: child.icon,
      tooltip: child.name,
      operation: child.operation,
      command: () =>
        router.push(child.router ? String(child.router) : "/fallback-route"),
    })),
    command: menu.router ? () => router.push(String(menu.router)) : undefined,
  }));
};

// 更新路由的 operation
const updateRouteOperation = (menus: Menu[]) => {
  menus.forEach((menu) => {
    if (menu.children) {
      menu.children.forEach((child) => {
        if (child.router) {
          const matchedRoute = router
            .getRoutes()
            .find((route) => route.path === child.router);
          if (matchedRoute) {
            matchedRoute.meta.operation = child.operation;
          }
        }
      });
    }
  });
};

onMounted(async () => {
  try {
    const response = await checkAuth();
    updateRouteOperation(response.data.menus);
    menuItems.value = transformMenus(
      response.data.menus
    ) as typeof menuItems.value;
  } catch (error) {
    console.error("Failed to fetch menus:", error);
  }
});

const handleLogout = async () => {
  try {
    await logout();
    userStore.clearUser();
    router.push("/login");
  } catch (error) {
    console.error("Logout failed:", error);
  }
};

const isActive = (item: any) => {
  // 判断当前菜单或子菜单的 router 是否与当前路由匹配
  if (item.command && typeof item.command === "function" && item.router) {
    return route.path === item.router;
  }
  if (item.items) {
    return item.items.some(
      (sub: any) => sub.router && route.path === sub.router
    );
  }
  return false;
};

// 面包屑导航数据
const breadcrumbItems = computed(() => {
  const items: Array<{ label: string; icon?: string; command?: () => void }> =
    [];
  const currentPath = route.path;
  const currentTitle = route.meta?.title;

  // 如果是根路径，不显示面包屑
  if (currentPath === "/" || currentPath === "") {
    return items;
  }

  // 根据路径生成面包屑
  if (currentPath.startsWith("/user-management/")) {
    items.push({
      label: "用户管理",
      icon: "pi pi-users",
      command: () => router.push("/user-management/roles"),
    });
    if (currentTitle) {
      items.push({ label: String(currentTitle) });
    }
  } else if (currentPath.startsWith("/customer-management/")) {
    items.push({
      label: "客户管理",
      icon: "pi pi-user",
      command: () => router.push("/customer-management/customers-info"),
    });
    if (currentTitle) {
      items.push({ label: String(currentTitle) });
    }
  } else if (currentPath.startsWith("/contract-management/")) {
    items.push({
      label: "合同管理",
      icon: "pi pi-file-edit",
      command: () => router.push("/contract-management/contracts-info"),
    });
    if (currentTitle) {
      items.push({ label: String(currentTitle) });
    }
  } else if (currentPath.startsWith("/order-management/")) {
    items.push({
      label: "订单管理",
      icon: "pi pi-shopping-cart",
      command: () => router.push("/order-management/orders-info"),
    });
    if (currentTitle) {
      items.push({ label: String(currentTitle) });
    }
  } else if (currentPath.startsWith("/account-management/")) {
    items.push({
      label: "账务管理",
      icon: "pi pi-calculator",
      command: () => router.push("/account-management/charge-details"),
    });
    if (currentTitle) {
      items.push({ label: String(currentTitle) });
    }
  }

  return items;
});

// 面包屑首页项
const breadcrumbHome = { icon: "pi pi-home", command: () => router.push("/") };

const calculatePopoverPosition = (event: MouseEvent, index: number) => {
  const target = event.currentTarget as HTMLElement;
  const rect = target.getBoundingClientRect();

  popoverPosition.value = {
    top: rect.top,
    left: rect.right + 5, // 5px 的间距
  };

  activeSubmenu.value = index;
};

const handleMenuClick = (item: any, index: number, event: MouseEvent) => {
  if (item.items && item.items.length) {
    if (sidebarCollapsed.value) {
      // 收起状态下，点击显示弹出子菜单
      calculatePopoverPosition(event, index);

      if (activeSubmenu.value === index) {
        // 如果点击的是当前打开的菜单，则关闭它
        activeSubmenu.value = null;
      } else {
        // 否则打开这个菜单
        activeSubmenu.value = index;
      }
    } else {
      // 展开状态下，切换子菜单的展开/收起状态
      if (!item._open) {
        menuItems.value.forEach((m: any) => {
          m._open = false;
        });
        item._open = true;
      } else {
        item._open = false;
      }
      // 强制触发响应式刷新
      menuItems.value = [...menuItems.value];
    }
  } else if (item.command) {
    // 如果是叶子节点，直接执行命令
    item.command();
    activeSubmenu.value = null; // 关闭任何打开的子菜单
  }
};

const handleSubMenuClick = (subItem: any) => {
  if (subItem.command) {
    subItem.command();
    activeSubmenu.value = null; // 点击后关闭子菜单
  }
};

const handleMouseEnter = (index: number, event: MouseEvent) => {
  if (sidebarCollapsed.value) {
    // 清除之前的定时器
    if (hoverTimer.value !== null) {
      clearTimeout(hoverTimer.value);
      hoverTimer.value = null;
    }

    hoveredItem.value = index;

    // 如果菜单项有子菜单，则显示弹出子菜单
    const item = menuItems.value[index];
    if (item && item.items && item.items.length > 0) {
      calculatePopoverPosition(event, index);
    }
  }
};

const handleMouseLeave = () => {
  if (sidebarCollapsed.value) {
    // 设置延时，避免鼠标移动到弹出菜单过程中菜单消失
    hoverTimer.value = window.setTimeout(() => {
      hoveredItem.value = null;
      // 只有当鼠标不在弹出菜单上时才关闭
      if (!document.querySelector(".submenu-popover:hover")) {
        activeSubmenu.value = null;
      }
    }, 300);
  }
};

const handlePopoverMouseEnter = () => {
  // 鼠标进入弹出菜单时清除关闭定时器
  if (hoverTimer.value !== null) {
    clearTimeout(hoverTimer.value);
    hoverTimer.value = null;
  }
};

const handlePopoverMouseLeave = () => {
  // 鼠标离开弹出菜单时关闭它
  activeSubmenu.value = null;
};

// 监听路由变化，关闭弹出菜单
watch(
  () => route.path,
  () => {
    activeSubmenu.value = null;
  }
);
</script>

<template>
  <div class="dashboard-container">
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-logo">
        <img src="../assets/accounting.svg" class="system-logo" />
      </div>
      <ul class="menu-list">
        <template v-for="(item, index) in menuItems" :key="item.label">
          <li
            class="menu-item"
            :class="{
              active: isActive(item),
              hovered: hoveredItem === index && sidebarCollapsed,
            }"
            @mouseenter="handleMouseEnter(index, $event)"
            @mouseleave="handleMouseLeave"
          >
            <div
              class="menu-link"
              @click="handleMenuClick(item, index, $event)"
              v-tooltip="sidebarCollapsed ? item.tooltip : ''"
            >
              <span :class="item.icon" class="menu-icon"></span>
              <span class="menu-text" v-if="!sidebarCollapsed">{{
                item.label
              }}</span>
              <span
                v-if="!sidebarCollapsed && item.items && item.items.length"
                class="submenu-arrow"
                :class="{ open: item._open }"
              >
                <svg
                  v-if="!item._open"
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M6 4L10 8L6 12"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <svg
                  v-else
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                >
                  <path
                    d="M4 6L8 10L12 6"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </span>
            </div>
            <!-- 展开状态下的子菜单 -->
            <transition name="submenu-fade">
              <ul
                v-if="
                  !sidebarCollapsed &&
                  item.items &&
                  item.items.length &&
                  item._open
                "
                class="submenu-list"
              >
                <li
                  v-for="sub in item.items"
                  :key="sub.label"
                  class="submenu-item"
                  :class="{ active: isActive(sub) }"
                  :title="sub.tooltip"
                >
                  <div class="menu-link" @click="sub.command && sub.command()">
                    <span :class="sub.icon" class="menu-icon"></span>
                    <span class="menu-text">{{ sub.label }}</span>
                  </div>
                </li>
              </ul>
            </transition>
          </li>
        </template>
      </ul>

      <!-- 收起状态下的弹出子菜单 -->
      <Teleport to="body">
        <transition name="popover-fade">
          <div
            v-if="
              sidebarCollapsed &&
              activeSubmenu !== null &&
              menuItems[activeSubmenu]?.items?.length > 0
            "
            class="submenu-popover"
            :style="{
              top: `${popoverPosition.top}px`,
              left: `${popoverPosition.left}px`,
            }"
            @mouseenter="handlePopoverMouseEnter"
            @mouseleave="handlePopoverMouseLeave"
          >
            <div class="popover-arrow"></div>
            <div class="popover-title">
              {{ menuItems[activeSubmenu].label }}
            </div>
            <ul class="popover-menu-list">
              <li
                v-for="sub in menuItems[activeSubmenu].items"
                :key="sub.label"
                class="popover-menu-item"
                :class="{ active: isActive(sub) }"
              >
                <div class="popover-menu-link" @click="handleSubMenuClick(sub)">
                  <span :class="sub.icon" class="menu-icon mr-2"></span>
                  <span class="menu-text">{{ sub.label }}</span>
                </div>
              </li>
            </ul>
          </div>
        </transition>
      </Teleport>
    </aside>

    <main
      class="main-content"
      :class="{ 'sidebar-collapsed': sidebarCollapsed }"
    >
      <header class="main-header">
        <div class="header-left">
          <Button
            icon="pi pi-bars"
            text
            rounded
            size="large"
            @click="sidebarCollapsed = !sidebarCollapsed"
            v-tooltip.right="sidebarCollapsed ? '展开菜单' : '收起菜单'"
            class="toggle-sidebar-btn"
          />
          <Breadcrumb
            v-if="breadcrumbItems.length > 0"
            :home="breadcrumbHome"
            :model="breadcrumbItems"
            class="breadcrumb-nav"
          />
        </div>
        <div class="user-menu">
          <Menu
            ref="menuRef"
            :model="[
              {
                label: userStore.user?.username,
                icon: 'pi pi-user',
                items: [
                  {
                    label: '退出登录',
                    icon: 'pi pi-sign-out',
                    command: handleLogout,
                  },
                ],
              },
            ]"
            popup
          />
          <Button
            icon="pi pi-user"
            text
            @click="(event) => (menuRef as any)?.toggle(event)"
          />
        </div>
      </header>

      <div class="content">
        <router-view></router-view>
      </div>

      <Footer />
    </main>
  </div>
</template>

<style scoped>
.dashboard-container {
  display: flex;
}

.sidebar {
  width: 250px;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  color: #222;
  transition: all 0.3s ease;
  height: 100vh;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 50px #d9ddfc;
  z-index: 100;
  flex-shrink: 0;
}

.sidebar.collapsed {
  width: 60px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  width: calc(100% - 250px);
}

.main-content.sidebar-collapsed {
  width: calc(100% - 60px);
}

@media (max-width: 991px) {
  .dashboard-container {
    flex-direction: column;
  }
  .sidebar {
    width: 100%;
    max-width: 100%;
    min-width: 0;
    position: absolute;
    z-index: 1000;
    height: 100vh;
    left: 0;
    top: 0;
  }
  .sidebar.collapsed {
    width: 60px;
  }
  .main-content,
  .main-content.sidebar-collapsed {
    width: 100%;
  }
}

.main-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3rem;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.toggle-sidebar-btn {
  color: #222;
  flex-shrink: 0;
}

.breadcrumb-nav {
  flex: 1;
  margin-left: 1rem;
}

.user-menu {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 面包屑导航样式 - 苹果设计风格 */
.breadcrumb-nav :deep(.p-breadcrumb) {
  background: transparent;
  border: none;
  padding: 0;
}

.breadcrumb-nav :deep(.p-breadcrumb-list) {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  list-style: none;
}

.breadcrumb-nav :deep(.p-breadcrumb-item) {
  display: flex;
  align-items: center;
}

.breadcrumb-nav :deep(.p-breadcrumb-item .p-breadcrumb-action) {
  color: #007aff;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.breadcrumb-nav :deep(.p-breadcrumb-item .p-breadcrumb-action:hover) {
  background-color: rgba(0, 122, 255, 0.1);
  color: #0056cc;
}

.breadcrumb-nav :deep(.p-breadcrumb-item:last-child .p-breadcrumb-action) {
  color: #1d1d1f;
  font-weight: 600;
  cursor: default;
}

.breadcrumb-nav
  :deep(.p-breadcrumb-item:last-child .p-breadcrumb-action:hover) {
  background-color: transparent;
  color: #1d1d1f;
}

.breadcrumb-nav :deep(.p-breadcrumb-separator) {
  color: #86868b;
  font-size: 0.875rem;
  margin: 0 0.25rem;
}

.breadcrumb-nav :deep(.p-breadcrumb-home .p-breadcrumb-action) {
  padding: 0.375rem;
  border-radius: 6px;
}

.breadcrumb-nav :deep(.p-breadcrumb-home .p-breadcrumb-action i) {
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 1rem;
    height: 56px;
  }

  .header-left {
    gap: 0.5rem;
  }

  .breadcrumb-nav {
    margin-left: 0.5rem;
  }

  .breadcrumb-nav :deep(.p-breadcrumb-item .p-breadcrumb-action) {
    font-size: 0.875rem;
    padding: 0.2rem 0.4rem;
  }

  .breadcrumb-nav :deep(.p-breadcrumb-separator) {
    font-size: 0.75rem;
    margin: 0 0.125rem;
  }
}

@media (max-width: 480px) {
  .breadcrumb-nav :deep(.p-breadcrumb-item:not(:last-child)) {
    display: none;
  }

  .breadcrumb-nav :deep(.p-breadcrumb-separator:not(:last-of-type)) {
    display: none;
  }
}

.menu-list {
  list-style: none;
  margin: 0;
  padding: 1rem 0.5rem;
  max-height: calc(100% - 60px);
  overflow-y: auto;
}

.menu-item,
.submenu-item {
  margin-bottom: 0.25rem;
  position: relative;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  color: #222;
  background: transparent;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

.menu-link:hover,
.menu-item.active > .menu-link,
.submenu-item.active > .menu-link,
.menu-item.hovered > .menu-link {
  background: #cbccce;
  color: #222;
}

.menu-item.active > .menu-link::before {
  content: "";
  position: absolute;
  left: 0;
  top: 8px;
  bottom: 8px;
  width: 4px;
  border-radius: 4px;
  background: #fff;
}

.menu-icon {
  font-size: 18px;
  color: green;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.sidebar:not(.collapsed) .menu-icon {
  margin-right: 10px;
}

.menu-text {
  font-size: 14px;
  flex: 1;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.submenu-arrow {
  display: flex;
  align-items: center;
  transition: transform 0.2s;
  color: #888;
}

.submenu-arrow.open {
  transform: rotate(90deg);
}

.submenu-list {
  margin: 0;
  padding: 4px 0 4px 25px;
  overflow: hidden;
  list-style: none;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  padding: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.system-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
  display: block;
}

.submenu-fade-enter-active,
.submenu-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 500px;
  opacity: 1;
  overflow: hidden;
}

.submenu-fade-enter-from,
.submenu-fade-leave-to {
  opacity: 0;
  max-height: 0;
}

/* 弹出子菜单样式 */
.submenu-popover {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 1000;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transform-origin: left center;
}

.popover-arrow {
  position: absolute;
  left: -6px;
  top: 20px;
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.95);
  transform: rotate(45deg);
  border-left: 1px solid rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.popover-title {
  padding: 12px 16px;
  font-weight: 500;
  color: green;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  font-size: 14px;
}

.popover-menu-list {
  list-style: none;
  margin: 0;
  padding: 8px;
}

.popover-menu-item {
  margin-bottom: 2px;
}

.popover-menu-link {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.popover-menu-link:hover,
.popover-menu-item.active .popover-menu-link {
  background-color: rgba(0, 113, 227, 0.1);
  color: green;
}

.popover-menu-item.active .popover-menu-link {
  font-weight: 500;
}

.popover-fade-enter-active,
.popover-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.popover-fade-enter-from,
.popover-fade-leave-to {
  opacity: 0;
  transform: scale(0.95) translateX(-10px);
}
</style>
