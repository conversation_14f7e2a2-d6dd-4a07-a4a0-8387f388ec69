<script setup lang="ts">
import { useToast } from "primevue/usetoast";
import { computed, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import {
  getCustomersSimpleList,
  getAccountSeqList,
  createAccountSeq,
  updateAccountSeq,
  AccountSeqParams,
  AccountSeqRequest,
} from "../../services/customer";
import type { AccountSeqInfo } from "../../types/customer";
import { formatDateTime } from "../../utils/common";

const accountSeqs = ref<AccountSeqInfo[]>();
const loading = ref(false);
const totalRecords = ref(0);
const toast = useToast();
const route = useRoute();

const hasOperationPermission = computed(() => {
  const currentRoute = route.matched[route.matched.length - 1];
  return currentRoute?.meta?.operation ?? false;
});

// 分页参数
const lazyParams = ref({
  page: 1,
  pageSize: 20,
});

// 客户筛选相关
const customerOptions = ref<{ label: string; value: number }[]>([]);
const selectedCustomerId = ref<number | null>(null);

// 筛选相关
const selectedFilterColumn = ref("");
const filterColumnOptions = [
  { label: "--", value: "--" },
  { label: "分账序号", value: "account_seq" },
  { label: "分账序号名称", value: "seq_name" },
];
const filterValue = ref("");

// 加载客户简单列表（用于下拉框）
const loadCustomerOptions = async () => {
  try {
    const response = await getCustomersSimpleList();
    customerOptions.value = response.data.map((item) => ({
      label: item.customer_name,
      value: item.id,
    }));

    // 如果有客户数据，默认选择第一个客户
    if (customerOptions.value.length > 0 && !selectedCustomerId.value) {
      selectedCustomerId.value = customerOptions.value[0].value;
    }
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载客户列表失败",
      life: 3000,
    });
  }
};

// 加载分账序号列表数据
const loadAccountSeqs = async () => {
  try {
    loading.value = true;
    const params: AccountSeqParams = {
      page: lazyParams.value.page,
      pageSize: lazyParams.value.pageSize,
    };

    if (selectedFilterColumn.value !== "--" && filterValue.value) {
      params.filter = filterValue.value;
      params.filterColumn = selectedFilterColumn.value;
    }

    const response = await getAccountSeqList(selectedCustomerId.value, params);
    accountSeqs.value = response.data.records;
    totalRecords.value = response.data.page.total;
  } catch (error) {
    toast.add({
      severity: "error",
      summary: "错误",
      detail: "加载分账序号列表失败",
      life: 3000,
    });
  } finally {
    loading.value = false;
  }
};

// 处理分页事件
const onPage = (event: { page: number; rows: number }) => {
  lazyParams.value.page = event.page + 1;
  lazyParams.value.pageSize = event.rows;
  loadAccountSeqs();
};

// 监听客户ID变化，重新加载分账序号列表
watch(selectedCustomerId, () => {
  lazyParams.value.page = 1; // 重置到第一页
  loadAccountSeqs();
});

onMounted(() => {
  loadCustomerOptions();
  loadAccountSeqs();
});

// 表单相关
const accountSeqDrawerVisible = ref(false);
const accountSeqForm = ref<AccountSeqRequest>({
  seq_name: "",
  account_seq: "",
});

// 字段错误信息
const fieldErrors = ref<{ [key: string]: string }>({});

// 打开新建对话框
const openNew = () => {
  if (!selectedCustomerId.value) {
    toast.add({
      severity: "warn",
      summary: "警告",
      detail: "请先选择客户",
      life: 3000,
    });
    return;
  }

  accountSeqForm.value = {
    seq_name: "",
    account_seq: "",
  };
  accountSeqDrawerVisible.value = true;
  fieldErrors.value = {};
};

// 打开编辑对话框
const openEdit = (accountSeq: AccountSeqInfo) => {
  accountSeqForm.value = {
    id: accountSeq.id,
    seq_name: accountSeq.seq_name,
    account_seq: accountSeq.account_seq,
  };
  accountSeqDrawerVisible.value = true;
  fieldErrors.value = {};
};

// 保存分账序号信息
const saveAccountSeq = async () => {
  if (!selectedCustomerId.value) {
    toast.add({
      severity: "warn",
      summary: "警告",
      detail: "请先选择客户",
      life: 3000,
    });
    return;
  }

  try {
    if (accountSeqForm.value.id) {
      // 编辑分账序号信息
      await updateAccountSeq(
        selectedCustomerId.value,
        accountSeqForm.value.id,
        accountSeqForm.value
      );
    } else {
      // 新增分账序号信息
      await createAccountSeq(selectedCustomerId.value, accountSeqForm.value);
    }
    accountSeqDrawerVisible.value = false;
    toast.add({
      severity: "success",
      summary: "成功",
      detail: accountSeqForm.value.id ? "分账序号更新成功" : "分账序号创建成功",
      life: 3000,
    });
    loadAccountSeqs();
  } catch (error: any) {
    if (error.response?.status === 422 && error.response.data.data.fields) {
      const fields = error.response.data.data.fields;
      // 清空旧错误
      fieldErrors.value = {};
      Object.keys(fields).forEach((key) => {
        fieldErrors.value[key] = fields[key]
          .map((item: any) => item.message)
          .join("; ");
      });
      toast.add({
        severity: "error",
        summary: "字段校验失败",
        detail: Object.values(fieldErrors.value).join("; "),
        life: 4000,
      });
    } else {
      toast.add({
        severity: "error",
        summary: "错误",
        detail: accountSeqForm.value.id
          ? "分账序号更新失败"
          : "分账序号创建失败",
        life: 3000,
      });
    }
  }
};
</script>

<template>
  <div class="account-seq-container">
    <Toast />
    <div class="card">
      <div class="card-header">
        <Message variant="simple" size="large">分账序号管理</Message>
        <Button
          label="新建"
          icon="pi pi-plus"
          @click="openNew"
          :disabled="!hasOperationPermission || !selectedCustomerId"
        />
      </div>

      <Toolbar class="mb-2">
        <template #start>
          <Select
            v-model="selectedCustomerId"
            :options="customerOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="请选择客户"
            filter
            style="width: 20rem"
          />
        </template>
        <template #end>
          <Select
            v-model="selectedFilterColumn"
            :options="filterColumnOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="请选择筛选字段"
            class="mr-2"
          />
          <FloatLabel class="mr-2">
            <InputText
              id="filterValue"
              v-model="filterValue"
              @keyup.enter="loadAccountSeqs"
              size="large"
            />
            <label for="filterValue">搜索值</label>
          </FloatLabel>
          <Button
            label="搜索"
            severity="success"
            icon="pi pi-search"
            @click="loadAccountSeqs"
          />
        </template>
      </Toolbar>

      <DataTable
        class="account-seq-table"
        :value="accountSeqs"
        :lazy="true"
        :paginator="true"
        :rows="20"
        :rowsPerPageOptions="[10, 20, 50]"
        :totalRecords="totalRecords"
        :loading="loading"
        @page="onPage($event)"
        stripedRows
        :scrollable="true"
      >
        <template #empty>
          <div class="empty-message">
            <i
              class="pi pi-inbox"
              style="
                font-size: 2rem;
                color: var(--p-text-color-secondary);
                margin-bottom: 1rem;
              "
            ></i>
            <p>暂无分账序号数据</p>
          </div>
        </template>
        <Column field="seq_name" header="分账序号名称" />
        <Column field="account_seq" header="分账序号" />
        <Column field="created_at" header="创建时间">
          <template #body="slotProps">
            <span>{{ formatDateTime(slotProps.data.created_at) }}</span>
          </template>
        </Column>
        <Column header="操作" :exportable="false" style="min-width: 8rem">
          <template #body="slotProps">
            <div style="display: flex; gap: 0.5rem">
              <Button
                icon="pi pi-pencil"
                outlined
                rounded
                severity="success"
                class="mr-2"
                :disabled="!hasOperationPermission"
                @click="openEdit(slotProps.data)"
                v-tooltip.top="'编辑分账序号'"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>
  </div>

  <!-- 新建/编辑分账序号抽屉 -->
  <Drawer
    v-model:visible="accountSeqDrawerVisible"
    position="right"
    :style="{ width: '70rem' }"
    :modal="true"
    :closable="true"
    :dismissable="true"
    :showCloseIcon="true"
    :header="accountSeqForm.id ? '编辑分账序号' : '新增分账序号'"
    class="p-fluid account-seq-drawer"
  >
    <div class="form-section">
      <div class="field">
        <label for="seq_name" class="required">
          分账序号名称
          <span v-if="fieldErrors.seq_name" class="p-error ml-2">{{
            fieldErrors.seq_name
          }}</span>
        </label>
        <InputText v-model="accountSeqForm.seq_name" required />
      </div>
      <div class="field">
        <label for="account_seq" class="required">
          分账序号
          <span v-if="fieldErrors.account_seq" class="p-error ml-2">{{
            fieldErrors.account_seq
          }}</span>
        </label>
        <InputText v-model="accountSeqForm.account_seq" required />
      </div>
    </div>

    <template #footer>
      <Button
        label="取消"
        icon="pi pi-times"
        @click="accountSeqDrawerVisible = false"
        class="p-button-text"
      />
      <Button label="保存" icon="pi pi-check" @click="saveAccountSeq" />
    </template>
  </Drawer>
</template>

<style scoped>
.account-seq-container {
  padding: 1rem;
  height: calc(100vh - 10rem);
}

.card {
  background: white;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 1px 3px 0 rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.empty-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: var(--surface-ground);
  border-radius: 6px;
}

.empty-message p {
  margin: 0;
  color: var(--text-color-secondary);
  font-size: 1.1rem;
}

.account-seq-drawer .form-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--surface-border);
}

.account-seq-drawer .form-section:last-child {
  border-bottom: none;
}

.account-seq-drawer label.required:after {
  content: "*";
  color: var(--red-500);
  margin-left: 0.25rem;
}

.p-drawer .field {
  margin-bottom: 1rem;
}

.p-drawer label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
}

.p-drawer .p-dropdown,
.p-drawer .p-select {
  width: 100%;
}

:deep(.p-error) {
  padding: 0.5rem;
  color: var(--red-500);
}

.account-seq-table {
  height: calc(100vh - 25rem);
  display: flex;
  flex-direction: column;
}
</style>
